enum CardSuit { spade, heart, club, diamond }

class CardRank {
  const CardRank._(this.value, this.label);
  final int value; // ordering for Big Two
  final String label;

  static const CardRank three = CardRank._(0, '3');
  static const CardRank four = CardRank._(1, '4');
  static const CardRank five = CardRank._(2, '5');
  static const CardRank six = CardRank._(3, '6');
  static const CardRank seven = CardRank._(4, '7');
  static const CardRank eight = CardRank._(5, '8');
  static const CardRank nine = CardRank._(6, '9');
  static const CardRank ten = CardRank._(7, '10');
  static const CardRank jack = CardRank._(8, 'J');
  static const CardRank queen = CardRank._(9, 'Q');
  static const CardRank king = CardRank._(10, 'K');
  static const CardRank ace = CardRank._(11, 'A');
  static const CardRank two = CardRank._(12, '2');

  static const List<CardRank> values = [
    three, four, five, six, seven, eight, nine, ten, jack, queen, king, ace, two
  ];
}

class PlayingCard {
  PlayingCard({required this.suit, required this.rank});
  final CardSuit suit;
  final CardRank rank;
}

class PlayerState {
  PlayerState({required this.id, required this.name});
  final String id;
  final String name;
  final List<PlayingCard> hand = [];
  List<PlayingCard> lastPlayed = []; // Track each player's last played cards
}

class BigTwoGameState {
  BigTwoGameState({required this.roomId, required this.players});
  final String roomId;
  final List<PlayerState> players;
  int currentTurnIndex = 0;
  List<PlayingCard> lastPlayed = [];

  PlayerState get currentPlayer => players[currentTurnIndex];
}


