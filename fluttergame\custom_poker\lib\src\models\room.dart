class RoomSettings {
  RoomSettings({required this.gameType, required this.playerCount, this.loserDrinks = true, this.cardsPerPlayer});

  final String gameType; // 'bigtwo', 'zhajinhua', 'custom'
  final int playerCount; // 2-6
  final bool loserDrinks;
  final int? cardsPerPlayer; // used when gameType == 'custom'
}

class PlayerIdentity {
  PlayerIdentity({required this.userId, required this.nickname, required this.isHost});
  final String userId;
  final String nickname;
  final bool isHost;
}


