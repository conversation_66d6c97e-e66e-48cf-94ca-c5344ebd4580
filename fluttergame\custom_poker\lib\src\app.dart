import 'package:flutter/material.dart';

import 'screens/lobby/lobby_screen.dart';
import 'screens/room/create_room_screen.dart';
import 'screens/room/join_room_screen.dart';
import 'screens/room/room_waiting_screen.dart';
import 'screens/game/game_screen.dart';

enum ThemeVariant { blue, green, white }

class CustomPokerApp extends StatefulWidget {
  const CustomPokerApp({super.key});

  static _CustomPokerAppState? of(BuildContext context) =>
      context.findAncestorStateOfType<_CustomPokerAppState>();

  @override
  State<CustomPokerApp> createState() => _CustomPokerAppState();
}

class _CustomPokerAppState extends State<CustomPokerApp> {
  ThemeVariant _variant = ThemeVariant.blue;

  void setTheme(ThemeVariant variant) {
    setState(() => _variant = variant);
  }

  @override
  Widget build(BuildContext context) {
    final Color seed = switch (_variant) {
      ThemeVariant.blue => Colors.blue,
      ThemeVariant.green => Colors.green,
      ThemeVariant.white => Colors.grey.shade400, // Use grey instead of white for better dark theme
    };

    final ColorScheme darkScheme = ColorScheme.fromSeed(
      seedColor: seed,
      brightness: Brightness.dark,
    );

    return MaterialApp(
      title: '酒桌游戏',
      theme: ThemeData(
        colorScheme: darkScheme,
        useMaterial3: true,
        scaffoldBackgroundColor: const Color(0xFF121212),
        appBarTheme: AppBarTheme(
          centerTitle: true,
          backgroundColor: darkScheme.surface,
          foregroundColor: darkScheme.onSurface,
        ),
        inputDecorationTheme: InputDecorationTheme(
          border: const OutlineInputBorder(),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: darkScheme.primary, width: 2),
          ),
        ),

      ),
      initialRoute: LobbyScreen.routeName,
      routes: {
        LobbyScreen.routeName: (_) => const LobbyScreen(),
        CreateRoomScreen.routeName: (_) => const CreateRoomScreen(),
        JoinRoomScreen.routeName: (_) => const JoinRoomScreen(),
        RoomWaitingScreen.routeName: (_) => const RoomWaitingScreen(),
        GameScreen.routeName: (_) => const GameScreen(),
      },
    );
  }
}


