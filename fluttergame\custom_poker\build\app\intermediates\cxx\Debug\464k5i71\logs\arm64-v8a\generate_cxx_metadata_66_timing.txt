# C/C++ build system timings
generate_cxx_metadata
  [gap of 64ms]
  create-invalidation-state 144ms
  [gap of 53ms]
  write-metadata-json-to-file 29ms
generate_cxx_metadata completed in 291ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 104ms]
  create-invalidation-state 194ms
  [gap of 68ms]
  write-metadata-json-to-file 29ms
generate_cxx_metadata completed in 396ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 237ms]
  create-invalidation-state 310ms
  [gap of 72ms]
  write-metadata-json-to-file 34ms
generate_cxx_metadata completed in 657ms

