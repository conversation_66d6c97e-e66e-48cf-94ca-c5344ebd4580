import 'package:flutter/material.dart';

import '../game/game_screen.dart';
import '../../models/room.dart';

class RoomWaitingScreen extends StatefulWidget {
  const RoomWaitingScreen({super.key});

  static const String routeName = '/room-waiting';

  @override
  State<RoomWaitingScreen> createState() => _RoomWaitingScreenState();
}

class _RoomWaitingScreenState extends State<RoomWaitingScreen> {
  bool _ready = false;

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    final String roomId = args?['roomId'] ?? '------';
    final PlayerIdentity? me = args?['me'] as PlayerIdentity?;
    final RoomSettings? settings = args?['settings'] as RoomSettings?;
    final bool isHost = me?.isHost == true;

    return Scaffold(
      appBar: AppBar(title: Text('房间 $roomId')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.groups),
                const SizedBox(width: 8),
                Text('${me?.nickname ?? '游客'} · ${isHost ? '房主' : '玩家'}'),
              ],
            ),
            const SizedBox(height: 16),
            if (settings != null)
              Text(
                '设置: ${settings.gameType} · ${settings.playerCount}人' +
                    (settings.cardsPerPlayer != null ? ' · 每人${settings.cardsPerPlayer}张' : '') +
                    ' · ${settings.loserDrinks ? '输喝' : '规则自定'}',
              ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('已准备'),
              value: _ready,
              onChanged: (v) => setState(() => _ready = v),
            ),
            const Spacer(),
            if (isHost)
              FilledButton.icon(
                onPressed: () {
                  Navigator.pushReplacementNamed(context, GameScreen.routeName, arguments: {
                    'roomId': roomId,
                    'settings': settings,
                    'me': me,
                  });
                },
                icon: const Icon(Icons.play_arrow),
                label: const Text('开始游戏'),
              )
            else
              const Text('等待房主开始游戏...'),
          ],
        ),
      ),
    );
  }
}


