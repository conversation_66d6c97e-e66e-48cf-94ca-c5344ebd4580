import 'dart:async';

/// A minimal mock interface to simulate realtime events for MVP wiring.
abstract class RealtimeService {
  Stream<Map<String, dynamic>> get events;
  Future<void> connect({required String roomId, required String userId});
  Future<void> send(Map<String, dynamic> message);
  Future<void> disconnect();
}

class MockRealtimeService implements RealtimeService {
  final StreamController<Map<String, dynamic>> _controller = StreamController.broadcast();

  @override
  Stream<Map<String, dynamic>> get events => _controller.stream;

  @override
  Future<void> connect({required String roomId, required String userId}) async {
    await Future<void>.delayed(const Duration(milliseconds: 200));
    _controller.add({'type': 'system', 'message': 'connected', 'roomId': roomId});
  }

  @override
  Future<void> send(Map<String, dynamic> message) async {
    _controller.add(message);
  }

  @override
  Future<void> disconnect() async {
    await _controller.close();
  }
}


