import 'package:flutter/material.dart';

import 'room_waiting_screen.dart';
import '../../models/room.dart';

class JoinRoomScreen extends StatefulWidget {
  const JoinRoomScreen({super.key});

  static const String routeName = '/join-room';

  @override
  State<JoinRoomScreen> createState() => _JoinRoomScreenState();
}

class _JoinRoomScreenState extends State<JoinRoomScreen> {
  final TextEditingController _roomIdController = TextEditingController();
  final TextEditingController _nicknameController = TextEditingController(text: '游客');

  @override
  void dispose() {
    _roomIdController.dispose();
    _nicknameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('加入房间')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: <PERSON>umn(
          children: [
            TextField(
              controller: _nicknameController,
              decoration: const InputDecoration(
                labelText: '昵称',
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: _roomIdController,
              decoration: const InputDecoration(
                labelText: '房间号',
                hintText: '输入6位房间号',
              ),
              keyboardType: TextInputType.number,
              maxLength: 6,
            ),
            const SizedBox(height: 12),
            FilledButton(
              onPressed: () {
                final roomId = _roomIdController.text.trim();
                if (roomId.length == 6) {
                  Navigator.pushNamed(
                    context,
                    RoomWaitingScreen.routeName,
                    arguments: {
                      'roomId': roomId,
                      'me': PlayerIdentity(
                        userId: 'u-guest',
                        nickname: _nicknameController.text.trim().isEmpty ? '游客' : _nicknameController.text.trim(),
                        isHost: false,
                      ),
                    },
                  );
                }
              },
              child: const Text('加入'),
            ),
          ],
        ),
      ),
    );
  }
}


