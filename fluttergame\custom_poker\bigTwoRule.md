# **Big Two (<PERSON>) Firebase Multiplayer Implementation Plan**

## **Game Overview**
- **Game Type**: Big Two (大老二) - Hong Kong style
- **Players**: 4 players (recommended), 3 works, 2 not recommended
- **Goal**: Be the first to play all your cards
- **Deck**: 52 cards, no Jokers

## **Card Ordering & Rules**
- **Rank (high → low)**: 2 > A > K > Q > J > 10 > 9 > ... > 4 > 3
- **Suit (high → low)**: Spades (♠) > Hearts (♥) > Clubs (♣) > Diamonds (♦)
- **Opening**: Player with ♦3 starts, first combo must include ♦3

## **Phase 1: Firebase Setup & Dependencies (Week 1)**

### **1.1 Firebase Project Setup**
```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Install FlutterFire CLI
dart pub global activate flutterfire_cli
```

### **1.2 Update pubspec.yaml Dependencies**
```yaml
# Add to your existing pubspec.yaml
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  
  # Firebase Core (Required)
  firebase_core: ^2.24.2
  
  # Firebase Services
  cloud_firestore: ^4.13.6
  firebase_auth: ^4.15.3
  
  # State Management
  provider: ^6.1.2  # You already have this
  
  # Networking & Connectivity
  connectivity_plus: ^5.0.2
  
  # Utilities
  uuid: ^4.2.1  # You already have this
  json_annotation: ^4.8.1
  
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  
  # Code Generation
  json_serializable: ^6.7.1
  build_runner: ^2.4.7
  
  # Testing
  test: ^1.24.0
  mockito: ^5.4.2
```

### **1.3 Firebase Configuration**
```bash
# From your project root
cd fluttergame/custom_poker
flutterfire configure
```

## **Phase 2: Big Two Game Logic Implementation (Week 1-2)**

### **2.1 Card System Enhancement**

**lib/src/models/big_two_card.dart**
```dart
enum BigTwoRank {
  three(0),    // Lowest
  four(1),
  five(2),
  six(3),
  seven(4),
  eight(5),
  nine(6),
  ten(7),
  jack(8),
  queen(9),
  king(10),
  ace(11),     // Second highest
  two(12);     // Highest
  
  const BigTwoRank(this.value);
  final int value;
}

enum BigTwoSuit {
  diamond(0),  // Lowest
  club(1),
  heart(2),
  spade(3);    // Highest
  
  const BigTwoSuit(this.value);
  final int value;
}

class BigTwoCard {
  final BigTwoRank rank;
  final BigTwoSuit suit;
  
  BigTwoCard({required this.rank, required this.suit});
  
  int get totalValue => rank.value * 4 + suit.value;
  
  bool operator >(BigTwoCard other) => totalValue > other.totalValue;
  bool operator <(BigTwoCard other) => totalValue < other.totalValue;
}
```

### **2.2 Combination Types**

**lib/src/game_logic/big_two_combinations.dart**
```dart
enum CombinationType {
  single,
  pair,
  straight,        // 5 cards consecutive
  flush,           // 5 cards same suit
  fullHouse,       // 3 of a kind + pair
  fourOfAKind,     // 4 of a kind + 1 kicker
  straightFlush,   // 5 cards consecutive same suit
}

class Combination {
  final CombinationType type;
  final List<BigTwoCard> cards;
  final int strength;  // For comparison
  
  Combination({
    required this.type,
    required this.cards,
    required this.strength,
  });
  
  bool canBeat(Combination other) {
    if (type != other.type) return false;
    return strength > other.strength;
  }
}
```

### **2.3 Combination Validator**

**lib/src/game_logic/combination_validator.dart**
```dart
class CombinationValidator {
  static Combination? validateAndCreate(List<BigTwoCard> cards) {
    if (cards.length == 1) {
      return _createSingle(cards);
    } else if (cards.length == 2) {
      return _validatePair(cards);
    } else if (cards.length == 5) {
      return _validateFiveCardHand(cards);
    }
    return null; // Invalid combination
  }
  
  static Combination _createSingle(List<BigTwoCard> cards) {
    return Combination(
      type: CombinationType.single,
      cards: cards,
      strength: cards.first.totalValue,
    );
  }
  
  static Combination? _validatePair(List<BigTwoCard> cards) {
    if (cards[0].rank != cards[1].rank) return null;
    
    // Strength is rank value * 4 + highest suit
    final highestSuit = cards.map((c) => c.suit.value).reduce(math.max);
    final strength = cards[0].rank.value * 4 + highestSuit;
    
    return Combination(
      type: CombinationType.pair,
      cards: cards,
      strength: strength,
    );
  }
  
  static Combination? _validateFiveCardHand(List<BigTwoCard> cards) {
    final sorted = List<BigTwoCard>.from(cards)
      ..sort((a, b) => a.rank.value.compareTo(b.rank.value));
    
    final isFlush = _isFlush(sorted);
    final isStraight = _isStraight(sorted);
    final isFullHouse = _isFullHouse(sorted);
    final isFourOfAKind = _isFourOfAKind(sorted);
    
    if (isStraight && isFlush) {
      return Combination(
        type: CombinationType.straightFlush,
        cards: cards,
        strength: _calculateStraightFlushStrength(sorted),
      );
    } else if (isFourOfAKind) {
      return Combination(
        type: CombinationType.fourOfAKind,
        cards: cards,
        strength: _calculateFourOfAKindStrength(sorted),
      );
    } else if (isFullHouse) {
      return Combination(
        type: CombinationType.fullHouse,
        cards: cards,
        strength: _calculateFullHouseStrength(sorted),
      );
    } else if (isFlush) {
      return Combination(
        type: CombinationType.flush,
        cards: cards,
        strength: _calculateFlushStrength(sorted),
      );
    } else if (isStraight) {
      return Combination(
        type: CombinationType.straight,
        cards: cards,
        strength: _calculateStraightStrength(sorted),
      );
    }
    
    return null; // Invalid 5-card combination
  }
  
  static bool _isStraight(List<BigTwoCard> sorted) {
    // 2s cannot be in straights, A is high only
    if (sorted.any((card) => card.rank == BigTwoRank.two)) return false;
    
    for (int i = 1; i < sorted.length; i++) {
      if (sorted[i].rank.value != sorted[i-1].rank.value + 1) {
        return false;
      }
    }
    return true;
  }
  
  static bool _isFlush(List<BigTwoCard> cards) {
    final suit = cards.first.suit;
    return cards.every((card) => card.suit == suit);
  }
  
  // Add other helper methods for full house, four of a kind, etc.
}
```

### **2.4 Game State Management**

**lib/src/game_logic/big_two_game_state.dart**
```dart
class BigTwoGameState {
  final String roomId;
  final List<BigTwoPlayer> players;
  final int currentPlayerIndex;
  final Combination? currentTrick;
  final List<bool> passedPlayers;
  final BigTwoCard? mustIncludeCard; // ♦3 for first trick
  final bool isFirstTrick;
  
  BigTwoGameState({
    required this.roomId,
    required this.players,
    this.currentPlayerIndex = 0,
    this.currentTrick,
    List<bool>? passedPlayers,
    this.mustIncludeCard,
    this.isFirstTrick = true,
  }) : passedPlayers = passedPlayers ?? List.filled(players.length, false);
  
  bool get isRoundComplete {
    final activePlayers = passedPlayers.where((passed) => !passed).length;
    return activePlayers <= 1;
  }
  
  BigTwoPlayer? get winner {
    return players.firstWhereOrNull((player) => player.hand.isEmpty);
  }
  
  BigTwoGameState nextTurn() {
    int nextIndex = currentPlayerIndex;
    do {
      nextIndex = (nextIndex + 1) % players.length;
    } while (passedPlayers[nextIndex] && !isRoundComplete);
    
    return copyWith(currentPlayerIndex: nextIndex);
  }
  
  BigTwoGameState playCards(List<BigTwoCard> cards) {
    final combination = CombinationValidator.validateAndCreate(cards);
    if (combination == null) return this; // Invalid play
    
    // Validate against current trick
    if (currentTrick != null && !combination.canBeat(currentTrick!)) {
      return this; // Cannot beat current trick
    }
    
    // Validate first trick rules
    if (isFirstTrick && mustIncludeCard != null) {
      if (!cards.contains(mustIncludeCard)) {
        return this; // Must include ♦3 in first trick
      }
    }
    
    // Update player hand
    final updatedPlayers = List<BigTwoPlayer>.from(players);
    updatedPlayers[currentPlayerIndex] = players[currentPlayerIndex].playCards(cards);
    
    return copyWith(
      players: updatedPlayers,
      currentTrick: combination,
      passedPlayers: List.filled(players.length, false), // Reset passes
      isFirstTrick: false,
    ).nextTurn();
  }
  
  BigTwoGameState pass() {
    final updatedPasses = List<bool>.from(passedPlayers);
    updatedPasses[currentPlayerIndex] = true;
    
    if (isRoundComplete) {
      // Start new trick, winner leads
      final leadPlayerIndex = _findTrickWinner();
      return copyWith(
        currentPlayerIndex: leadPlayerIndex,
        currentTrick: null,
        passedPlayers: List.filled(players.length, false),
        mustIncludeCard: null,
      );
    }
    
    return copyWith(passedPlayers: updatedPasses).nextTurn();
  }
  
  int _findTrickWinner() {
    // Find the player who played the current trick
    for (int i = 0; i < players.length; i++) {
      if (!passedPlayers[i]) return i;
    }
    return currentPlayerIndex;
  }
}

class BigTwoPlayer {
  final String id;
  final String name;
  final List<BigTwoCard> hand;
  final bool isOnline;
  
  BigTwoPlayer({
    required this.id,
    required this.name,
    required this.hand,
    this.isOnline = true,
  });
  
  BigTwoPlayer playCards(List<BigTwoCard> cards) {
    final newHand = List<BigTwoCard>.from(hand);
    for (final card in cards) {
      newHand.remove(card);
    }
    return copyWith(hand: newHand);
  }
  
  bool hasCard(BigTwoCard card) {
    return hand.any((c) => c.rank == card.rank && c.suit == card.suit);
  }
}
```

### **2.5 Game Rules Engine**

**lib/src/game_logic/big_two_rules.dart**
```dart
class BigTwoRules {
  static const int MIN_PLAYERS = 2;
  static const int MAX_PLAYERS = 4;
  static const int RECOMMENDED_PLAYERS = 4;
  static const int CARDS_PER_PLAYER_4P = 13;
  static const int CARDS_PER_PLAYER_3P = 17;
  
  static bool canStartGame(List<BigTwoPlayer> players) {
    return players.length >= MIN_PLAYERS && players.length <= MAX_PLAYERS;
  }
  
  static BigTwoCard get diamondThree {
    return BigTwoCard(rank: BigTwoRank.three, suit: BigTwoSuit.diamond);
  }
  
  static int findStartingPlayer(List<BigTwoPlayer> players) {
    for (int i = 0; i < players.length; i++) {
      if (players[i].hasCard(diamondThree)) {
        return i;
      }
    }
    return 0; // Fallback
  }
  
  static List<BigTwoCard> createDeck() {
    final deck = <BigTwoCard>[];
    for (final suit in BigTwoSuit.values) {
      for (final rank in BigTwoRank.values) {
        deck.add(BigTwoCard(rank: rank, suit: suit));
      }
    }
    return deck;
  }
  
  static void dealCards(List<BigTwoPlayer> players, List<BigTwoCard> deck) {
    deck.shuffle();
    
    final cardsPerPlayer = players.length == 4 ? CARDS_PER_PLAYER_4P : CARDS_PER_PLAYER_3P;
    
    for (int cardIndex = 0; cardIndex < cardsPerPlayer; cardIndex++) {
      for (int playerIndex = 0; playerIndex < players.length; playerIndex++) {
        final card = deck[cardIndex * players.length + playerIndex];
        players[playerIndex].hand.add(card);
      }
    }
    
    // Sort each player's hand
    for (final player in players) {
      player.hand.sort((a, b) => a.totalValue.compareTo(b.totalValue));
    }
  }
  
  static bool isValidPlay(List<BigTwoCard> cards, BigTwoGameState gameState) {
    // Check if combination is valid
    final combination = CombinationValidator.validateAndCreate(cards);
    if (combination == null) return false;
    
    // Check if can beat current trick
    if (gameState.currentTrick != null) {
      if (!combination.canBeat(gameState.currentTrick!)) return false;
    }
    
    // Check first trick rules
    if (gameState.isFirstTrick && gameState.mustIncludeCard != null) {
      if (!cards.contains(gameState.mustIncludeCard)) return false;
    }
    
    // Check if player has all the cards
    final currentPlayer = gameState.players[gameState.currentPlayerIndex];
    for (final card in cards) {
      if (!currentPlayer.hasCard(card)) return false;
    }
    
    return true;
  }
  
  static int calculateScore(BigTwoPlayer player) {
    // Basic scoring: 1 point per remaining card
    // Can be enhanced with weights for 2s and Aces
    return player.hand.length;
  }
}
```

## **Phase 3: Network Integration (Week 2)**

### **3.1 Firebase Game Actions**

**lib/src/services/big_two_game_service.dart**
```dart
class BigTwoGameService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  Future<void> createGame(String roomId, List<String> playerIds) async {
    final players = playerIds.map((id) => BigTwoPlayer(
      id: id,
      name: 'Player $id',
      hand: [],
    )).toList();
    
    final deck = BigTwoRules.createDeck();
    BigTwoRules.dealCards(players, deck);
    
    final startingPlayerIndex = BigTwoRules.findStartingPlayer(players);
    
    final gameState = BigTwoGameState(
      roomId: roomId,
      players: players,
      currentPlayerIndex: startingPlayerIndex,
      mustIncludeCard: BigTwoRules.diamondThree,
      isFirstTrick: true,
    );
    
    await _firestore.collection('games').doc(roomId).set({
      'gameState': gameState.toJson(),
      'status': 'playing',
      'createdAt': FieldValue.serverTimestamp(),
    });
  }
  
  Future<void> playCards(String roomId, String playerId, List<BigTwoCard> cards) async {
    await _firestore.runTransaction((transaction) async {
      final gameDoc = await transaction.get(_firestore.collection('games').doc(roomId));
      final gameState = BigTwoGameState.fromJson(gameDoc.data()!['gameState']);
      
      // Validate it's the player's turn
      if (gameState.players[gameState.currentPlayerIndex].id != playerId) {
        throw Exception('Not your turn');
      }
      
      // Validate the play
      if (!BigTwoRules.isValidPlay(cards, gameState)) {
        throw Exception('Invalid play');
      }
      
      final newGameState = gameState.playCards(cards);
      
      // Log the action
      await transaction.set(
        _firestore.collection('games').doc(roomId).collection('actions').doc(),
        {
          'type': 'play_cards',
          'playerId': playerId,
          'cards': cards.map((c) => c.toJson()).toList(),
          'timestamp': FieldValue.serverTimestamp(),
        },
      );
      
      // Update game state
      await transaction.update(_firestore.collection('games').doc(roomId), {
        'gameState': newGameState.toJson(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      // Check for winner
      if (newGameState.winner != null) {
        await transaction.update(_firestore.collection('games').doc(roomId), {
          'status': 'finished',
          'winner': newGameState.winner!.id,
          'finishedAt': FieldValue.serverTimestamp(),
        });
      }
    });
  }
  
  Future<void> pass(String roomId, String playerId) async {
    await _firestore.runTransaction((transaction) async {
      final gameDoc = await transaction.get(_firestore.collection('games').doc(roomId));
      final gameState = BigTwoGameState.fromJson(gameDoc.data()!['gameState']);
      
      // Validate it's the player's turn
      if (gameState.players[gameState.currentPlayerIndex].id != playerId) {
        throw Exception('Not your turn');
      }
      
      final newGameState = gameState.pass();
      
      // Log the action
      await transaction.set(
        _firestore.collection('games').doc(roomId).collection('actions').doc(),
        {
          'type': 'pass',
          'playerId': playerId,
          'timestamp': FieldValue.serverTimestamp(),
        },
      );
      
      // Update game state
      await transaction.update(_firestore.collection('games').doc(roomId), {
        'gameState': newGameState.toJson(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    });
  }
}
```

## **Phase 4: UI Implementation (Week 3)**

### **4.1 Game Screen Updates**

**Key UI Components:**
- **Current Trick Display**: Show the cards currently on the table
- **Hand Selection**: Allow multi-select for combinations
- **Combination Validator**: Real-time validation of selected cards
- **Turn Indicator**: Clear indication of whose turn it is
- **Pass Button**: Allow players to pass their turn
- **Game Rules Display**: Show valid combinations and rules

### **4.2 Card Selection Logic**

```dart
class CardSelectionManager {
  final Set<BigTwoCard> _selectedCards = {};
  
  void toggleCard(BigTwoCard card) {
    if (_selectedCards.contains(card)) {
      _selectedCards.remove(card);
    } else {
      _selectedCards.add(card);
    }
  }
  
  bool isValidSelection() {
    return CombinationValidator.validateAndCreate(_selectedCards.toList()) != null;
  }
  
  CombinationType? getSelectionType() {
    final combination = CombinationValidator.validateAndCreate(_selectedCards.toList());
    return combination?.type;
  }
}
```

## **Phase 5: Testing Strategy (Week 3-4)**

### **5.1 Game Logic Testing**
```dart
// test/big_two_logic_test.dart
void main() {
  group('Big Two Combinations', () {
    test('Single card validation', () {
      final card = BigTwoCard(rank: BigTwoRank.ace, suit: BigTwoSuit.spade);
      final combination = CombinationValidator.validateAndCreate([card]);
      expect(combination?.type, CombinationType.single);
    });
    
    test('Pair validation', () {
      final cards = [
        BigTwoCard(rank: BigTwoRank.king, suit: BigTwoSuit.spade),
        BigTwoCard(rank: BigTwoRank.king, suit: BigTwoSuit.heart),
      ];
      final combination = CombinationValidator.validateAndCreate(cards);
      expect(combination?.type, CombinationType.pair);
    });
    
    test('Straight validation', () {
      final cards = [
        BigTwoCard(rank: BigTwoRank.three, suit: BigTwoSuit.spade),
        BigTwoCard(rank: BigTwoRank.four, suit: BigTwoSuit.heart),
        BigTwoCard(rank: BigTwoRank.five, suit: BigTwoSuit.club),
        BigTwoCard(rank: BigTwoRank.six, suit: BigTwoSuit.diamond),
        BigTwoCard(rank: BigTwoRank.seven, suit: BigTwoSuit.spade),
      ];
      final combination = CombinationValidator.validateAndCreate(cards);
      expect(combination?.type, CombinationType.straight);
    });
    
    test('Invalid straight with 2', () {
      final cards = [
        BigTwoCard(rank: BigTwoRank.ace, suit: BigTwoSuit.spade),
        BigTwoCard(rank: BigTwoRank.two, suit: BigTwoSuit.heart),
        BigTwoCard(rank: BigTwoRank.three, suit: BigTwoSuit.club),
        BigTwoCard(rank: BigTwoRank.four, suit: BigTwoSuit.diamond),
        BigTwoCard(rank: BigTwoRank.five, suit: BigTwoSuit.spade),
      ];
      final combination = CombinationValidator.validateAndCreate(cards);
      expect(combination, isNull);
    });
  });
  
  group('Game Rules', () {
    test('Find starting player with ♦3', () {
      final players = [
        BigTwoPlayer(id: '1', name: 'P1', hand: [
          BigTwoCard(rank: BigTwoRank.ace, suit: BigTwoSuit.spade),
        ]),
        BigTwoPlayer(id: '2', name: 'P2', hand: [
          BigTwoCard(rank: BigTwoRank.three, suit: BigTwoSuit.diamond),
        ]),
      ];
      
      final startingIndex = BigTwoRules.findStartingPlayer(players);
      expect(startingIndex, 1);
    });
  });
}
```

## **Phase 6: Deployment & Polish (Week 4)**

### **6.1 Room Configuration**
```json
{
  "variant": "hk_big_two",
  "players": 4,
  "opening": {
    "must_include_diamond_3": true,
    "first_trick_single_only": false,
    "allow_five_card_opening": true
  },
  "combos": {
    "allow_three_of_a_kind_as_play": false,
    "straight_allows_2": false,
    "ace_low_in_straight": false
  },
  "suit_order": ["S", "H", "C", "D"],
  "rank_order_high_to_low": ["2","A","K","Q","J","10","9","8","7","6","5","4","3"],
  "timer_seconds": 30,
  "pass_locks_until_new_trick": true,
  "scoring": {
    "mode": "card_count", 
    "weights": {"A": 1, "2": 2},
    "drinking_mode": false
  }
}
```

### **6.2 Firebase Security Rules**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /games/{gameId} {
      allow read, write: if request.auth != null;
      
      match /actions/{actionId} {
        allow read, write: if request.auth != null;
      }
    }
  }
}
```

## **Development Timeline**
- **Week 1**: Firebase setup, Big Two card system, combination validation
- **Week 2**: Game state management, network integration, basic UI
- **Week 3**: Complete UI implementation, testing, debugging
- **Week 4**: Polish, deployment, advanced features (scoring, room options)

## **Key Features**
- ✅ **Authentic Big Two Rules**: Hong Kong style with proper card ordering
- ✅ **Real-time Multiplayer**: Firebase-powered synchronization
- ✅ **Combination Validation**: All 7 valid combination types
- ✅ **Turn Management**: Proper passing and trick resolution
- ✅ **First Trick Rules**: ♦3 requirement enforcement
- ✅ **Scoring System**: Multiple scoring modes
- ✅ **Room Configuration**: Customizable game rules

This implementation plan provides a complete roadmap for building an authentic Big Two game with proper rules enforcement and real-time multiplayer capabilities.
