import 'package:flutter/material.dart';
import '../../models/bigtwo_models.dart';
import '../../models/room.dart';

class GameScreen extends StatefulWidget {
  const GameScreen({super.key});

  static const String routeName = '/game';

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> with TickerProviderStateMixin {
  bool _showDrinkPrompt = false;
  String _drinkPromptText = '';
  BigTwoGameState? _game;
  final bool _stacked = true;
  final Set<PlayingCard> _selected = <PlayingCard>{};
  final ScrollController _playersScrollController = ScrollController();
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _playersScrollController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _scrollToCurrentPlayer() {
    if (_game == null || !_playersScrollController.hasClients) return;

    final currentPlayerIndex = _game!.currentTurnIndex;
    const double playerCardWidth = 90.0;
    const double playerCardMargin = 6.0;
    const double totalPlayerWidth = playerCardWidth + playerCardMargin;

    final double targetScrollPosition = currentPlayerIndex * totalPlayerWidth -
        (_playersScrollController.position.viewportDimension / 2) + (playerCardWidth / 2);

    final double maxScrollExtent = _playersScrollController.position.maxScrollExtent;
    final double clampedPosition = targetScrollPosition.clamp(0.0, maxScrollExtent);

    _playersScrollController.animateTo(
      clampedPosition,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    final String roomId = args?['roomId'] ?? '------';
    final RoomSettings? settings = args?['settings'] as RoomSettings?;
    final PlayerIdentity? me = args?['me'] as PlayerIdentity?;

    _ensureDealtOnce(roomId: roomId, settings: settings, me: me);

    return Scaffold(
      appBar: AppBar(
        title: Text('游戏中 - 房间 $roomId'),
        actions: [],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFF0F172A),
              const Color(0xFF1E293B),
            ],
          ),
        ),
        child: Column(
          children: [
            if (_showDrinkPrompt)
              MaterialBanner(
                content: Text(_drinkPromptText),
                actions: [
                  TextButton(onPressed: () => setState(() => _showDrinkPrompt = false), child: const Text('知道了')),
                ],
              ),
            Expanded(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  // Calculate responsive sizing
                  final double availableHeight = constraints.maxHeight;
                  final double screenWidth = constraints.maxWidth;
                  final bool isSmallScreen = availableHeight < 600 || screenWidth < 800;
                  final double playersHeight = isSmallScreen ? 80 : 100;
                  final double tableCardsPadding = isSmallScreen ? 12 : 16;
                  final double handViewPadding = isSmallScreen ? 12 : 16;
                  
                  return Padding(
                    padding: EdgeInsets.all(isSmallScreen ? 12.0 : 16.0),
                    child: Column(
                      children: [
                        if (_game != null) ...[
                          // Show other players - horizontal scrollable
                          if (_game!.players.length > 1)
                            SizedBox(
                              height: playersHeight,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(left: 4, bottom: 6),
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.group,
                                          size: 14,
                                          color: const Color(0xFF94A3B8),
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          '其他玩家',
                                          style: TextStyle(
                                            color: const Color(0xFFE2E8F0),
                                            fontSize: 12,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Expanded(
                                    child: ListView.builder(
                                      controller: _playersScrollController,
                                      scrollDirection: Axis.horizontal,
                                      itemCount: _game!.players.length,
                                      itemBuilder: (context, index) {
                                        final player = _game!.players[index];
                                        final isCurrentTurn = _game!.currentTurnIndex == index;

                                        return Container(
                                          padding: const EdgeInsets.symmetric(horizontal: 4.0),
                                          child: AnimatedBuilder(
                                            animation: _pulseAnimation,
                                            builder: (context, child) {
                                              return Transform.scale(
                                                scale: isCurrentTurn ? _pulseAnimation.value : 1.0,
                                                child: _PlayerCard(
                                                  player: player,
                                                  isCurrentTurn: isCurrentTurn,
                                                  isSmallScreen: isSmallScreen,
                                                ),
                                              );
                                            },
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          SizedBox(height: isSmallScreen ? 8 : 10),
                          // Table cards
                          if (_game!.lastPlayed.isNotEmpty)
                            Flexible(
                              child: Container(
                                padding: EdgeInsets.all(tableCardsPadding),
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      const Color(0xFF059669).withValues(alpha: 0.15),
                                      const Color(0xFF047857).withValues(alpha: 0.25),
                                    ],
                                  ),
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: const Color(0xFF10B981).withValues(alpha: 0.4),
                                    width: 2,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(alpha: 0.4),
                                      blurRadius: 12,
                                      offset: const Offset(0, 6),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.casino,
                                          size: 20,
                                          color: const Color(0xFF34D399),
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          '桌面',
                                          style: TextStyle(
                                            color: const Color(0xFFE2E8F0),
                                            fontSize: 16,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 12),
                                    Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: const Color(0xFF1E293B).withValues(alpha: 0.6),
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                          color: const Color(0xFF10B981).withValues(alpha: 0.3),
                                          width: 1,
                                        ),
                                      ),
                                      child: _CardsRow(cards: _game!.lastPlayed, stacked: _stacked),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                        ],
                        // Use Flexible instead of Spacer for better control
                        Flexible(
                          child: Container(),
                        ),
                        // Player's hand
                        if (_game != null)
                          Container(
                            padding: EdgeInsets.all(handViewPadding),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const SizedBox(height: 12),
                                _HandView(
                                  cards: _game!.currentPlayer.hand,
                                  stacked: _stacked,
                                  selected: _selected,
                                  onToggle: _toggleSelect,
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  );
                },
              ),
            ),
            SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    SizedBox(
                      width: 120,
                      height: 48,
                      child: OutlinedButton(onPressed: _onPlay, child: const Text('出牌')),
                    ),
                    const SizedBox(width: 12),
                    SizedBox(
                      width: 120,
                      height: 48,
                      child: OutlinedButton(onPressed: _onPass, child: const Text('跳过')),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _ensureDealtOnce({required String roomId, RoomSettings? settings, PlayerIdentity? me}) {
    if (_game != null) return;
    final int playerCount = settings?.playerCount ?? 4;
    final List<PlayerState> players = List.generate(playerCount, (i) {
      final String name = i == 0 ? (me?.nickname ?? '我') : '玩家${String.fromCharCode(0x41 + i)}';
      return PlayerState(id: 'p$i', name: name);
    });
    final BigTwoGameState game = BigTwoGameState(roomId: roomId, players: players);
    _deal(game, settings: settings);
    _sortHands(game);
    setState(() {
      _game = game;
    });

    WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToCurrentPlayer());
  }

  void _deal(BigTwoGameState game, {RoomSettings? settings}) {
    final List<PlayingCard> deck = _createDeck();
    deck.shuffle();
    int turn = 0;
    final int? targetPerPlayer = settings?.cardsPerPlayer;
    final int maxCards = targetPerPlayer != null ? (targetPerPlayer * game.players.length) : deck.length;
    for (int i = 0; i < maxCards && i < deck.length; i++) {
      final card = deck[i];
      game.players[turn].hand.add(card);
      turn = (turn + 1) % game.players.length;
    }
  }

  void _sortHands(BigTwoGameState game) {
    for (final p in game.players) {
      p.hand.sort(_bigtwoComparatorDesc);
    }
  }

  int _bigtwoComparatorDesc(PlayingCard a, PlayingCard b) {
    if (a.rank.value != b.rank.value) {
      return b.rank.value.compareTo(a.rank.value);
    }
    int suitWeight(CardSuit s) {
      switch (s) {
        case CardSuit.diamond:
          return 0;
        case CardSuit.club:
          return 1;
        case CardSuit.heart:
          return 2;
        case CardSuit.spade:
          return 3;
      }
    }
    return suitWeight(b.suit).compareTo(suitWeight(a.suit));
  }

  List<PlayingCard> _createDeck() {
    final List<PlayingCard> deck = [];
    for (final suit in CardSuit.values) {
      for (final rank in CardRank.values) {
        deck.add(PlayingCard(suit: suit, rank: rank));
      }
    }
    return deck;
  }

  void _onPlay() {
    if (_game == null) return;
    if (_selected.isEmpty) return;
    final List<PlayingCard> toPlay = _selected.toList();
    setState(() {
      _game!.currentPlayer.hand.removeWhere((c) => _selected.contains(c));
      _game!.currentPlayer.lastPlayed = toPlay;
      _game!.lastPlayed = toPlay;
      _selected.clear();
      _game!.currentTurnIndex = (_game!.currentTurnIndex + 1) % _game!.players.length;
    });
    WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToCurrentPlayer());
  }

  void _onPass() {
    if (_game == null) return;
    setState(() {
      _game!.currentTurnIndex = (_game!.currentTurnIndex + 1) % _game!.players.length;
    });
    WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToCurrentPlayer());
  }

  void _toggleSelect(PlayingCard card) {
    setState(() {
      if (_selected.contains(card)) {
        _selected.remove(card);
      } else {
        _selected.add(card);
      }
    });
  }
}

class _PlayerCard extends StatelessWidget {
  const _PlayerCard({
    required this.player,
    required this.isCurrentTurn,
    required this.isSmallScreen,
  });

  final PlayerState player;
  final bool isCurrentTurn;
  final bool isSmallScreen;

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 600),
      curve: Curves.easeInOut,
      width: 90,
      margin: const EdgeInsets.symmetric(horizontal: 2),
      decoration: BoxDecoration(
        color: isCurrentTurn
            ? const Color(0xFF3B82F6).withValues(alpha: 0.25)
            : const Color(0xFF334155).withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isCurrentTurn
              ? const Color(0xFF60A5FA).withValues(alpha: 0.8)
              : const Color(0xFF64748B).withValues(alpha: 0.2),
          width: isCurrentTurn ? 3 : 1,
        ),
        boxShadow: isCurrentTurn ? [
          BoxShadow(
            color: const Color(0xFF3B82F6).withValues(alpha: 0.4),
            blurRadius: 8,
            spreadRadius: 2,
            offset: const Offset(0, 2),
          ),
        ] : [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(4),
        child: Row(
          children: [
            // Left side: Player icon and name
            Expanded(
              flex: 2,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(3),
                    decoration: BoxDecoration(
                      color: isCurrentTurn
                          ? const Color(0xFF3B82F6).withValues(alpha: 0.4)
                          : const Color(0xFF1E293B).withValues(alpha: 0.6),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(
                      Icons.person,
                      size: isCurrentTurn ? 16 : 14,
                      color: isCurrentTurn
                          ? const Color(0xFFFFFFFF)
                          : const Color(0xFF94A3B8),
                    ),
                  ),
                  const SizedBox(height: 2),
                  Flexible(
                    child: Text(
                      player.name,
                      style: TextStyle(
                        color: isCurrentTurn
                            ? const Color(0xFFFFFFFF)
                            : const Color(0xFFE2E8F0),
                        fontSize: isCurrentTurn ? 9 : 8,
                        fontWeight: isCurrentTurn ? FontWeight.w700 : FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 4),
            // Right side: Poker card shape with count
            Container(
              width: 22,
              height: 30,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withValues(alpha: 0.9),
                    Colors.white.withValues(alpha: 0.7),
                  ],
                ),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: const Color(0xFF64748B).withValues(alpha: 0.4),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 2,
                    offset: const Offset(1, 1),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  '${player.hand.length}',
                  style: const TextStyle(
                    color: Color(0xFF1E293B),
                    fontSize: 10,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _CardTile extends StatelessWidget {
  const _CardTile({required this.card, this.selected = false});
  final PlayingCard card;
  final bool selected;

  @override
  Widget build(BuildContext context) {
    final ColorScheme scheme = Theme.of(context).colorScheme;
    final bool isDark = Theme.of(context).brightness == Brightness.dark;
    final bool isRed = card.suit == CardSuit.heart || card.suit == CardSuit.diamond;
    final Color ink = isRed ? Colors.red.shade700 : (isDark ? const Color.fromARGB(255, 9, 9, 9) : Colors.black87);
    final Color cardColor = isDark ? const Color(0xFFF4F6F8) : Colors.white;

    // Responsive sizing based on screen size
    final Size screenSize = MediaQuery.of(context).size;
    final bool isSmallScreen = screenSize.height < 600 || screenSize.width < 800;
    final double cardWidth = isSmallScreen ? 65 : 80;
    final double cardHeight = isSmallScreen ? 100 : 120;
    final double liftOffset = isSmallScreen ? -8.0 : -12.0;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
      transform: Matrix4.identity()..translate(0.0, selected ? liftOffset : 0.0),
      width: cardWidth,
      height: cardHeight,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: cardColor,
        border: Border.all(
          color: selected ? scheme.primary : (isDark ? Colors.black54 : Colors.black26),
          width: selected ? 3 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.5 : 0.15),
            blurRadius: 6,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Top-left rank + suit
          Positioned(
            top: 6,
            left: 6,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  card.rank.label,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 14 : 18,
                    fontWeight: FontWeight.w800,
                    color: ink,
                  ),
                ),
                Text(
                  _suitSymbol(card.suit),
                  style: TextStyle(
                    fontSize: isSmallScreen ? 11 : 14,
                    color: ink,
                  ),
                ),
              ],
            ),
          ),
          // Bottom-right mirrored
          Positioned(
            bottom: 6,
            right: 6,
            child: Transform.rotate(
              angle: 3.14159,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    card.rank.label,
                    style: TextStyle(
                      fontSize: isSmallScreen ? 14 : 18,
                      fontWeight: FontWeight.w800,
                      color: ink,
                    ),
                  ),
                  Text(
                    _suitSymbol(card.suit),
                    style: TextStyle(
                      fontSize: isSmallScreen ? 11 : 14,
                      color: ink,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Center watermark suit
          Center(
            child: Text(
              _suitSymbol(card.suit),
              style: TextStyle(
                fontSize: isSmallScreen ? 36 : 48,
                color: (isDark ? Colors.white : Colors.black).withValues(alpha: isDark ? 0.10 : 0.06),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _suitSymbol(CardSuit suit) {
    switch (suit) {
      case CardSuit.spade:
        return '♠';
      case CardSuit.heart:
        return '♥';
      case CardSuit.club:
        return '♣';
      case CardSuit.diamond:
        return '♦';
    }
  }
}

class _HandView extends StatelessWidget {
  const _HandView({required this.cards, required this.stacked, required this.selected, required this.onToggle});
  final List<PlayingCard> cards;
  final bool stacked;
  final Set<PlayingCard> selected;
  final void Function(PlayingCard card) onToggle;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Responsive sizing based on available space
        final bool isSmallScreen = constraints.maxHeight < 200 || constraints.maxWidth < 600;
        final double cardHeight = isSmallScreen ? 100 : 120;

        if (!stacked) {
          final int cardsPerRow = isSmallScreen ? 8 : 10;
          final double rowOverlap = cardHeight * 0.2;

          List<List<PlayingCard>> cardRows = [];
          for (int i = 0; i < cards.length; i += cardsPerRow) {
            final end = (i + cardsPerRow < cards.length) ? i + cardsPerRow : cards.length;
            cardRows.add(cards.sublist(i, end));
          }

          if (cardRows.isEmpty) {
            return const SizedBox.shrink();
          }

          final double totalHeight = cardHeight + (cardRows.length - 1) * (cardHeight - rowOverlap) + 28;

          return Container(
            width: constraints.maxWidth,
            height: totalHeight,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Stack(
              children: [
                for (int rowIndex = 0; rowIndex < cardRows.length; rowIndex++)
                  Positioned(
                    top: rowIndex * (cardHeight - rowOverlap),
                    left: 0,
                    right: 0,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        for (int cardIndex = 0; cardIndex < cardRows[rowIndex].length; cardIndex++) ...[
                          if (cardIndex > 0) const SizedBox(width: 8),
                          GestureDetector(
                            onTap: () => onToggle(cardRows[rowIndex][cardIndex]),
                            child: _CardTile(
                              card: cardRows[rowIndex][cardIndex],
                              selected: selected.contains(cardRows[rowIndex][cardIndex]),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
              ],
            ),
          );
        }

        final double overlap = isSmallScreen ? 20 : 26;
        final int cardsPerRow = isSmallScreen ? 7 : 9;
        final double rowOverlap = cardHeight * 0.3;

        List<List<PlayingCard>> cardRows = [];
        for (int i = 0; i < cards.length; i += cardsPerRow) {
          final end = (i + cardsPerRow < cards.length) ? i + cardsPerRow : cards.length;
          cardRows.add(cards.sublist(i, end));
        }

        if (cardRows.isEmpty) {
          return const SizedBox.shrink();
        }

        final double totalHeight = cardHeight + (cardRows.length - 1) * (cardHeight - rowOverlap) + 38;

        return Container(
          width: constraints.maxWidth,
          height: totalHeight,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Stack(
            children: [
              for (int rowIndex = 0; rowIndex < cardRows.length; rowIndex++)
                Positioned(
                  top: rowIndex * (cardHeight - rowOverlap),
                  left: 0,
                  right: 0,
                  child: Center(
                    child: SizedBox(
                      height: cardHeight,
                      child: Stack(
                        children: [
                          for (int cardIndex = 0; cardIndex < cardRows[rowIndex].length; cardIndex++)
                            Positioned(
                              left: cardIndex * overlap,
                              child: GestureDetector(
                                onTap: () => onToggle(cardRows[rowIndex][cardIndex]),
                                child: _CardTile(
                                  card: cardRows[rowIndex][cardIndex],
                                  selected: selected.contains(cardRows[rowIndex][cardIndex]),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}

class _CardsRow extends StatelessWidget {
  const _CardsRow({required this.cards, this.stacked = false});
  final List<PlayingCard> cards;
  final bool stacked;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (!stacked) {
          const int cardsPerRow = 10;
          List<Widget> rows = [];

          for (int i = 0; i < cards.length; i += cardsPerRow) {
            final end = (i + cardsPerRow < cards.length) ? i + cardsPerRow : cards.length;
            final rowCards = cards.sublist(i, end);

            rows.add(
              Wrap(
                spacing: 8,
                alignment: WrapAlignment.center,
                children: rowCards.map((card) => _CardTile(card: card)).toList(),
              ),
            );

            if (end < cards.length) {
              rows.add(const SizedBox(height: 8));
            }
          }

          return Container(
            width: constraints.maxWidth,
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 8),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: rows,
            ),
          );
        }

        const double cardWidth = 84;
        const double cardHeight = 118;
        const double overlap = 26;
        final double width = cards.isEmpty ? 0 : cardWidth + overlap * (cards.length - 1);

        return Container(
          width: constraints.maxWidth,
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 8),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: SizedBox(
              width: width,
              height: cardHeight,
              child: Stack(
                children: [
                  for (int i = 0; i < cards.length; i++)
                    Positioned(
                      left: i * overlap,
                      child: _CardTile(card: cards[i]),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
