import 'package:flutter/material.dart';

import 'room_waiting_screen.dart';
import '../../models/room.dart';
import '../../utils/id.dart';

class CreateRoomScreen extends StatefulWidget {
  const CreateRoomScreen({super.key});

  static const String routeName = '/create-room';

  @override
  State<CreateRoomScreen> createState() => _CreateRoomScreenState();
}

class _CreateRoomScreenState extends State<CreateRoomScreen> {
  int _playerCount = 4;
  String _gameType = 'bigtwo';
  bool _loserDrinks = true;
  final TextEditingController _nicknameController = TextEditingController(text: '游客');
  int _cardsPerPlayer = 13;

  @override
  void dispose() {
    _nicknameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('创建房间')),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          TextField(
            controller: _nicknameController,
            decoration: const InputDecoration(labelText: '昵称'),
          ),
          const SizedBox(height: 12),
          DropdownButtonFormField<String>(
            value: _gameType,
            items: const [
              DropdownMenuItem(value: 'bigtwo', child: Text('大老二')),
              DropdownMenuItem(value: 'zhajinhua', child: Text('炸金花')),
              DropdownMenuItem(value: 'custom', child: Text('自定义')),
            ],
            onChanged: (v) => setState(() => _gameType = v ?? 'bigtwo'),
            decoration: const InputDecoration(labelText: '游戏类型'),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              const Text('玩家人数: '),
              Expanded(
                child: Slider(
                  min: 2,
                  max: 6,
                  divisions: 4,
                  value: _playerCount.toDouble(),
                  label: '$_playerCount',
                  onChanged: (v) => setState(() => _playerCount = v.toInt()),
                ),
              ),
              Text('$_playerCount 人'),
            ],
          ),
          SwitchListTile(
            title: const Text('输的人喝酒'),
            value: _loserDrinks,
            onChanged: (v) => setState(() => _loserDrinks = v),
          ),
          if (_gameType == 'custom') ...[
            const SizedBox(height: 12),
            Row(
              children: [
                const Text('每人发牌数: '),
                Expanded(
                  child: Slider(
                    min: 1,
                    max: 20,
                    divisions: 19,
                    value: _cardsPerPlayer.toDouble(),
                    label: '$_cardsPerPlayer',
                    onChanged: (v) => setState(() => _cardsPerPlayer = v.toInt()),
                  ),
                ),
                Text('$_cardsPerPlayer'),
              ],
            ),
          ],
          const SizedBox(height: 24),
          FilledButton(
            onPressed: () {
              final String roomId = generateRoomId();
              final settings = RoomSettings(
                gameType: _gameType,
                playerCount: _playerCount,
                loserDrinks: _loserDrinks,
                cardsPerPlayer: _gameType == 'custom' ? _cardsPerPlayer : null,
              );
              final me = PlayerIdentity(
                userId: generateUserId(),
                nickname: _nicknameController.text.trim().isEmpty ? '游客' : _nicknameController.text.trim(),
                isHost: true,
              );
              Navigator.pushNamed(context, RoomWaitingScreen.routeName, arguments: {
                'roomId': roomId,
                'me': me,
                'settings': settings,
              });
            },
            child: const Text('创建'),
          ),
        ],
      ),
    );
  }
}


