import 'package:flutter/material.dart';

import '../room/create_room_screen.dart';
import '../room/join_room_screen.dart';
import '../../widgets/app_logo.dart';
import '../../app.dart';

class LobbyScreen extends StatelessWidget {
  const LobbyScreen({super.key});

  static const String routeName = '/';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('大厅'),
        actions: [

          PopupMenuButton<String>(
            onSelected: (value) {
              final app = CustomPokerApp.of(context);
              if (app == null) return;
              switch (value) {
                case 'blue':
                  app.setTheme(ThemeVariant.blue);
                  break;
                case 'green':
                  app.setTheme(ThemeVariant.green);
                  break;
                case 'white':
                  app.setTheme(ThemeVariant.white);
                  break;
              }
            },
            itemBuilder: (context) => const [
              PopupMenuItem(value: 'blue', child: Text('蓝色')),
              PopupMenuItem(value: 'green', child: Text('绿色')),
              PopupMenuItem(value: 'white', child: Text('白色')),
            ],
            icon: const Icon(Icons.color_lens_outlined),
            tooltip: '主题',
          ),
        ],
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const AppLogo(size: 84),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: () => Navigator.pushNamed(context, CreateRoomScreen.routeName),
                icon: const Icon(Icons.add_circle_outline),
                label: const Text('创建房间'),
              ),
              const SizedBox(height: 16),
              FilledButton.icon(
                onPressed: () => Navigator.pushNamed(context, JoinRoomScreen.routeName),
                icon: const Icon(Icons.login),
                label: const Text('加入房间'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}


